---
title: Agents
slug: /components-agents
---

Langflow's **Agent** and **MCP Tools** components are critical for building agentic flows.
These components define the behavior and capabilities of AI agents in your flows.

<details>
<summary>How agents work</summary>

Agents extend Large Language Models (LLMs) by integrating _tools_, which are functions that provide additional context and enable autonomous task execution.
These integrations make agents more specialized and powerful than standalone LLMs.

Whereas an LLM might generate acceptable, inert responses to general queries and tasks, an agent can leverage the integrated context and tools to provide more relevant responses and even take action.
For example, you might create an agent that can access your company's knowledge base, repositories, and other resources to help your team with tasks that require knowledge of your specific products, customers, and code.

Agents use LLMs as a reasoning engine to process input, determine which actions to take to address the query, and then generate a response.
The response could be a typical text-based LLM response, or it could involve an action, like editing a file, running a script, or calling an external API.

In an agentic context, tools are functions that the agent can run to perform tasks or access external resources.
A function is wrapped as a `Tool` object with a common interface that the agent understands.
Agents become aware of tools through tool registration, which is when the agent is provided a list of available tools typically at agent initialization.
The `Tool` object's description tells the agent what the tool can do so that it can decide whether the tool is appropriate for a given request.

</details>

## Examples of agentic flows

For examples of agentic flows using the **Agent** and **MCP Tools** components, see the following:

* [**Simple Agent** template](/simple-agent): Create a basic agentic flow in Langflow with an **Agent** component that can use two other Langflow components as tools.
The LLM specified in the **Agent** component's settings can use its own built-in functionality as well as the functionality provided by the connected tools when generating responses.

* [Langflow quickstart](/get-started-quickstart): Modify the **Simple Agent** template to use different tools, and then learn how to use an agentic flow in an application.

* [Use an agent as a tool](/agents-tools#use-an-agent-as-a-tool): Create a multi-agent flow.

* [Use Langflow as an MCP client](/mcp-client) and [Use Langflow as an MCP server](/mcp-server): Use the **Agent** and **MCP Tools** components to implement the Model Context Protocol (MCP) in your flows.

## Agent component {#agent-component}

The **Agent** component is the primary agent actor in your agentic flows.
This component uses an LLM integration to respond to input, such as a chat message or file upload.

The agent can use the tools already available in the base LLM model as well as additional tools that you connect to the **Agent** component's **Tools** port.
You can connect any Langflow component as a tool, including other **Agent** components and MCP servers through the [**MCP Tools** component](#mcp-connection).

For more information about using this component, see [Use Langflow agents](/agents).

## MCP Tools component {#mcp-connection}

The **MCP Tools** component connects to a [Model Context Protocol (MCP)](https://modelcontextprotocol.io/introduction) server and exposes the MCP server's functions as tools for Langflow agents to use to respond to input.

In addition to publicly available MCP servers and your own custom-built MCP servers, you can connect Langflow MCP servers, which allow your agent to use your Langflow flows as tools.
To do this, use the **MCP Tools** component's [SSE mode](/mcp-client#mcp-sse-mode) to connect to your Langflow MCP server at the `/api/v1/mcp/sse` endpoint.

For more information about using this component and serving flows as MCP tools, see [Use Langflow as an MCP client](/mcp-client) and [Use Langflow as an MCP server](/mcp-server).

<details>
<summary>Earlier versions of the MCP Tools component</summary>

* In Langflow version 1.5, the **MCP Connection** component was renamed to the **MCP Tools** component.
* In Langflow version 1.3, the **MCP Tools (stdio)** and **MCP Tools (SSE)** components were deprecated and replaced by the unified **MCP Connection** component, which was later renamed to **MCP Tools**.

</details>

## Legacy agent components

The following components are legacy components.
You can still use these components in your flows, but they are no longer maintained and they can be removed in future releases.

Replace these components with the **Agent** component or other Langflow components, depending on your use case.

* **CrewAI Hierarchical Task**
* **CrewAI Sequential Task**

<details>
<summary>CrewAI Agent</summary>

This component represents CrewAI agents, allowing for the creation of specialized AI agents with defined roles goals and capabilities within a crew.
For more information, see the [CrewAI agents documentation](https://docs.crewai.com/core-concepts/Agents/).

This component accepts the following parameters:

| Name | Display Name | Info |
|------|--------------|------|
| role | Role | Input parameter. The role of the agent. |
| goal | Goal | Input parameter. The objective of the agent. |
| backstory | Backstory | Input parameter. The backstory of the agent. |
| tools | Tools | Input parameter. The tools at the agent's disposal. |
| llm | Language Model | Input parameter. The language model that runs the agent. |
| memory | Memory | Input parameter. This determines whether the agent should have memory or not. |
| verbose | Verbose | Input parameter. This enables verbose output. |
| allow_delegation | Allow Delegation | Input parameter. This determines whether the agent is allowed to delegate tasks to other agents. |
| allow_code_execution | Allow Code Execution | Input parameter. This determines whether the agent is allowed to execute code. |
| kwargs | kwargs | Input parameter. Additional keyword arguments for the agent. |
| output | Agent | Output parameter. The constructed CrewAI Agent object. |

</details>

<details>
<summary>CrewAI Hierarchical Crew</summary>

This component represents a group of agents managing how they should collaborate and the tasks they should perform in a hierarchical structure. This component allows for the creation of a crew with a manager overseeing the task execution.
For more information, see the [CrewAI hierarchical crew documentation](https://docs.crewai.com/how-to/Hierarchical/).

It accepts the following parameters:

| Name | Display Name | Info |
|------|--------------|------|
| agents | Agents | Input parameter. The list of Agent objects representing the crew members. |
| tasks | Tasks | Input parameter. The list of HierarchicalTask objects representing the tasks to be executed. |
| manager_llm | Manager LLM | Input parameter. The language model for the manager agent. |
| manager_agent | Manager Agent | Input parameter. The specific agent to act as the manager. |
| verbose | Verbose | Input parameter. This enables verbose output for detailed logging. |
| memory | Memory | Input parameter. The memory configuration for the crew. |
| use_cache | Use Cache | Input parameter. This enables caching of results. |
| max_rpm | Max RPM | Input parameter. This sets the maximum requests per minute. |
| share_crew | Share Crew | Input parameter. This determines if the crew information is shared among agents. |
| function_calling_llm | Function Calling LLM | Input parameter. The language model for function calling. |
| crew | Crew | Output parameter. The constructed Crew object with hierarchical task execution. |

</details>

<details>
<summary>CrewAI Sequential Crew</summary>

This component represents a group of agents with tasks that are executed sequentially. This component allows for the creation of a crew that performs tasks in a specific order.
For more information, see the [CrewAI sequential crew documentation](https://docs.crewai.com/how-to/Sequential/).

It accepts the following parameters:

| Name | Display Name | Info |
|------|--------------|------|
| tasks | Tasks | Input parameter. The list of SequentialTask objects representing the tasks to be executed. |
| verbose | Verbose | Input parameter. This enables verbose output for detailed logging. |
| memory | Memory | Input parameter. The memory configuration for the crew. |
| use_cache | Use Cache | Input parameter. This enables caching of results. |
| max_rpm | Max RPM | Input parameter. This sets the maximum requests per minute. |
| share_crew | Share Crew | Input parameter. This determines if the crew information is shared among agents. |
| function_calling_llm | Function Calling LLM | Input parameter. The language model for function calling. |
| crew | Crew | Output parameter. The constructed Crew object with sequential task execution. |

</details>

<details>
<summary>CrewAI Sequential Task Agent</summary>

This component creates a CrewAI Task and its associated Agent allowing for the definition of sequential tasks with specific agent roles and capabilities.
For more information, see the [CrewAI sequential agents documentation](https://docs.crewai.com/how-to/Sequential/).

It accepts the following parameters:

| Name | Display Name | Info |
|------|--------------|------|
| role | Role | Input parameter. The role of the agent. |
| goal | Goal | Input parameter. The objective of the agent. |
| backstory | Backstory | Input parameter. The backstory of the agent. |
| tools | Tools | Input parameter. The tools at the agent's disposal. |
| llm | Language Model | Input parameter. The language model that runs the agent. |
| memory | Memory | Input parameter. This determines whether the agent should have memory or not. |
| verbose | Verbose | Input parameter. This enables verbose output. |
| allow_delegation | Allow Delegation | Input parameter. This determines whether the agent is allowed to delegate tasks to other agents. |
| allow_code_execution | Allow Code Execution | Input parameter. This determines whether the agent is allowed to execute code. |
| agent_kwargs | Agent kwargs | Input parameter. The additional kwargs for the agent. |
| task_description | Task Description | Input parameter. The descriptive text detailing the task's purpose and execution. |
| expected_output | Expected Task Output | Input parameter. The clear definition of the expected task outcome. |
| async_execution | Async Execution | Input parameter. The boolean flag indicating asynchronous task execution. |
| previous_task | Previous Task | Input parameter. The previous task in the sequence for chaining. |
| task_output | Sequential Task | Output parameter. The list of SequentialTask objects representing the created tasks. |

</details>

## See also

* [**Message History** component](/components-helpers#message-history)
* [Store chat memory](/memory#store-chat-memory)
* [Bundles](/components-bundle-components)
* [Legacy LangChain components](/bundles-langchain#legacy-langchain-components)