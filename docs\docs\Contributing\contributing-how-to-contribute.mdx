---
title: Contribute to Lang<PERSON>
slug: /contributing-how-to-contribute
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

This guide is intended to help you start contributing to Langflow.
As an open-source project in a rapidly developing field, Langflow welcomes contributions, whether it be in the form of a new feature, improved infrastructure, or better documentation.

To contribute code or documentation to this project, follow the [fork and pull request](https://docs.github.com/en/get-started/quickstart/contributing-to-projects) workflow.

## Install Langflow from source

Install Langflow from source by forking the repository, and then set up your development environment using Make.

### Prerequisites

* [uv](https://docs.astral.sh/uv/getting-started/installation/) version 0.4 or later
* [Node.js](https://nodejs.org/en/download/package-manager)
* [Make](https://www.gnu.org/software/make/#documentation) (Linux and macOS only)

### Clone the Langflow repository

1. Fork the [Langflow GitHub repository](https://github.com/langflow-ai/langflow).

2. Add the new remote to your local repository on your local machine:
```bash
git remote add FORK_NAME https://github.com/GIT_USERNAME/langflow.git
```
Replace the following:
* `FORK_NAME`: A name for your fork of the repository
* `GIT_USERNAME`: Your Git username

### Run Langflow from source

You can run Langflow from source after cloning the repository, even if you're not contributing to the codebase.

<details>
<summary>Run from source on macOS/Linux</summary>

In your terminal, navigate to the root of the Langflow directory, and then run `make run_cli`.

This command does the following:

- Installs frontend and backend dependencies
- Builds the frontend static files
- Starts the application with default settings

The Langflow frontend is served at `http://localhost:7860`.

</details>

<details>
<summary>Run from source with Windows CMD</summary>

To run Langflow from source on Windows, you can use the Langflow project's included scripts, or run the commands in the terminal.

Do one of the following:

* To install and run Langflow with the included Windows Batch file, navigate to the `scripts/windows` directory, and then run the `build_and_run.bat` file.

* To run Langflow from the Windows Command Line:

    1. Build the Langflow frontend:

        ```
        cd src/frontend && npm install && npm run build && npm run start
        ```

    2. Copy the contents of the built `src/frontend/build` directory to `src/backend/base/langflow/frontend`.

    3. Start Langflow:

        ```
        uv run langflow run
        ```

The Langflow frontend is served at `http://localhost:7860`.

</details>

<details>
<summary>Run from source with Powershell</summary>

To run Langflow from source on Windows, you can use the Langflow project's included scripts, or run the commands in the terminal.

Do one of the following:

* To install and run Langflow with the included scripts, navigate to the `scripts/windows` directory, and then run the `build_and_run.ps1` file.

* To run Langflow from a Powershell terminal:

    1. Run the following commands separately to build the Langflow frontend:

        ```
        cd src/frontend
        npm install
        npm run build
        npm run start
        ```

    2. Copy the contents of the built `src/frontend/build` directory to `src/backend/base/langflow/frontend`.

    3. Start Langflow:

        ```
        uv run langflow run
        ```

The Langflow frontend is served at `http://localhost:7860`.

</details>

### Set up your Langflow development environment

<Tabs>
<TabItem value="linux-macos" label="Linux or macOS" default>

1. Set up the Langflow development environment:

    ```bash
    make init
    ```

    This command sets up the development environment by doing the following:

    - Checking for uv and npm.
    - Installing backend and frontend dependencies.
    - Installing pre-commit hooks.

2. Run the backend and frontend in separate terminals for development:

    ```bash
    # Run backend in development mode (includes hot reload)
    make backend
    ```

    ```bash
    # In another terminal, run frontend in development mode (includes hot reload)
    make frontend
    ```

    The `make backend` and `make frontend` commands automatically install dependencies, so you don't need to run install commands separately.

    The frontend is served at `http://localhost:7860`.

3. Optional: Install pre-commit hooks to help keep your changes clean and well-formatted.

    With pre-commit hooks installed, you must use `uv run git commit` instead of `git commit` directly.

    `make init` installs pre-commit hooks automatically, or you can run the following command to install them manually:

    ```bash
    uv sync
    uv run pre-commit install
    ```

4. To test your changes before pushing commits, run `make lint`, `make format`, and `make unit_tests`.
To run all tests, including coverage, unit, and integration, tests, run `make tests`.

</TabItem>
<TabItem value="Windows" label="Windows">

Since Windows does not include `make`, building and running Langflow from source uses `npm` and `uv`.

To set up the Langflow development environment, run the frontend and backend in separate terminals:

1. To run the frontend, run the following commands:

    ```bash
    cd src/frontend
    npm install
    npm run start
    ```

2. In a separate terminal, run the following command to run the backend:

    ```bash
    uv run langflow run --backend-only
    ```

The frontend is served at `http://localhost:7860`.

</TabItem>
</Tabs>

### Debug

The repo includes a `.vscode/launch.json` file for debugging the backend in VSCode, which is faster than debugging with Docker Compose. To debug Langflow with the `launch.json` file in VSCode:

For more information, see the [VSCode documentation](https://code.visualstudio.com/docs/debugtest/debugging#_start-a-debugging-session).

### Additional contribution guides

- [Contribute bundles](./contributing-bundles.mdx)
- [Contribute components](./contributing-components.mdx)
- [Contribute tests](./contributing-component-tests.mdx)
- [Contribute templates](./contributing-templates.mdx)

## Contribute documentation

The documentation is built using [Docusaurus](https://docusaurus.io/) and written in [Markdown](https://docusaurus.io/docs/markdown-features).
For style guidance, see the [Google Developer Documentation Style Guide](https://developers.google.com/style).

1. Install [Node.js](https://nodejs.org/en/download/package-manager) and [Yarn](https://yarnpkg.com/)

2. Fork the [Langflow GitHub repository](https://github.com/langflow-ai/langflow).

3. Add the new remote to your local repository on your local machine:

    ```bash
    git remote add FORK_NAME https://github.com/GIT_USERNAME/langflow.git
    ```

    Replace the following:

    * `FORK_NAME`: A name for your fork of the repository
    * `GIT_USERNAME`: Your Git username

4. From the root of your local Langflow fork, change to the `/docs` directory:

    ```bash
    cd docs
    ```

5. Install dependencies and start a local Docusaurus static site with hot reload:

    ```bash
    yarn install
    yarn start
    ```

    The documentation is served at `localhost:3000`.

6. To edit and create content, work with the `.mdx` files in the `langflow/docs/docs` directory.

    Create new files in `.mdx` format.

    Navigation is defined in `langflow/docs/sidebars.js`.

    Most pages use a `slug` for shorthand cross-referencing, rather than supplying the full or relative directory path.
    For example, if a page has a `slug` of `/cool-page`, you can link to it with `[Cool page](/cool-page)` from any other `/docs` page.

7. Recommended: After making some changes, run `yarn build` to build the site locally with more robust logging.
This can help you find broken links before creating a PR.

## Open a pull request

To submit a pull request, do the following:

1. Open a new GitHub pull request with your patch against the `main` branch.
2. Ensure the PR title follows semantic commit conventions. For example, features are `feat: add new feature` and fixes are `fix: correct issue with X`.

Some additional guidance on pull request titles:
* Ensure the pull request description clearly describes the problem and solution. If the PR fixes an issue, include a link to the fixed issue in the PR description with `Fixes #1234`.
* Pull request titles appear in Langflow's release notes, so they should explain what the PR does as explicitly as possible.
* Pull requests should strive to fix one thing **only**, and should contain a good description of what is being fixed.

3. A Langflow maintainer will review your pull request and may request changes, so ensure you pay attention to your PRs. Thanks for your contribution!

For more information, see the [Python Developer's Guide](https://devguide.python.org/getting-started/pull-request-lifecycle/index.html#making-good-commits).