---
title: Deploy Langflow on Render
slug: /deployment-render
---

This guide explains how to deploy Langflow on [Render](https://render.com/), a cloud platform for deploying web applications and APIs.

1. Prepare a Render instance that can support Langflow.

   Langflow requires at least 2 GB of RAM to run, so you must use a Render instance type of **Standard** or better.
   This requires a paid Render account.
   For more information, see [Render Web Services](https://render.com/docs/web-services) and [Render pricing](https://render.com/pricing).

2. Follow this link to start a Langflow deployment on Render:

   [![Deploy to Render](/logos/render-deploy.svg)](https://render.com/deploy?repo=https%3A%2F%2Fgithub.com%2Flangflow-ai%2Flangflow%2Ftree%2Fdev)

3. Enter a blueprint name, select the branch for your `render.yaml` file, and then click **Deploy Blueprint**.

When deployment is complete, your Langflow instance is ready to use.