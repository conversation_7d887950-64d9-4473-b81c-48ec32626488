---
title: DuckDuckGo
slug: /bundles-duckduckgo
---

import Icon from "@site/src/components/icon";

[Bundles](/components-bundle-components) contain custom components that support specific third-party integrations with Langflow.

This page describes the components that are available in the **DuckDuckGo** bundle.

## DuckDuckGo Search

This component performs web searches using the [DuckDuckGo](https://www.duckduckgo.com) search engine with result-limiting capabilities.

It outputs a list of search results as a [`DataFrame`](/data-types#dataframe) with a `text` key containing the search results as a single string.

### DuckDuckGo Search parameters

Some **DuckDuckGo Search** component input parameters are hidden by default in the visual editor.
You can toggle parameters through the <Icon name="SlidersHorizontal" aria-hidden="true"/> **Controls** in the [component's header menu](/concepts-components#component-menus).

| Name | Type | Description |
|------|------|-------------|
| input_value | String | Input parameter. The search query to execute with DuckDuckGo. |
| max_results | Integer | Input parameter. The maximum number of search results to return. Default: 5. |
| max_snippet_length | Integer | Input parameter. The maximum length of each result snippet. Default: 100. |

## See also

* [**Web Search** component](/components-data#web-search)
* [**SearchApi** bundle](/bundles-searchapi)