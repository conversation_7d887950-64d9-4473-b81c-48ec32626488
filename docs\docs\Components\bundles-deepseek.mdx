---
title: DeepSeek
slug: /bundles-deepseek
---

import Icon from "@site/src/components/icon";

[Bundles](/components-bundle-components) contain custom components that support specific third-party integrations with Langflow.

This page describes the components that are available in the **DeepSeek** bundle.

For more information about DeepSeek features and functionality used by DeepSeek components, see the [DeepSeek documentation](https://api-docs.deepseek.com/).

## DeepSeek text generation

The **DeepSeek** component generates text using DeepSeek's language models.

It can output either a **Model Response** ([`Message`](/data-types#message)) or a **Language Model** ([`LanguageModel`](/data-types#languagemodel)).

Use the **Language Model** output when you want to use a DeepSeek model as the LLM for another LLM-driven component, such as a **Language Model** or **Smart Function** component.

For more information, see [**Language Model** components](/components-models).

### DeepSeek text generation parameters

Many **DeepSeek** component input parameters are hidden by default in the visual editor.
You can toggle parameters through the <Icon name="SlidersHorizontal" aria-hidden="true"/> **Controls** in the [component's header menu](/concepts-components#component-menus).

| Name | Type | Description |
|------|------|-------------|
| max_tokens | Integer | Input parameter. Maximum number of tokens to generate. Set to `0` for unlimited. Range: `0-128000`. |
| model_kwargs | Dictionary | Input parameter. Additional keyword arguments for the model. |
| json_mode | Boolean | Input parameter. If `True`, outputs JSON regardless of passing a schema. |
| model_name | String | Input parameter. The DeepSeek model to use. Default: `deepseek-chat`. |
| api_base | String | Input parameter. Base URL for API requests. Default: `https://api.deepseek.com`. |
| api_key | SecretString | Input parameter. Your DeepSeek API key for authentication. |
| temperature | Float | Input parameter. Controls randomness in responses. Range: `[0.0, 2.0]`. Default: `1.0`. |
| seed | Integer | Input parameter. Number initialized for random number generation. Use the same seed integer for more reproducible results, and use a different seed number for more random results. |