---
title: Deploy a public Langflow server
slug: /deployment-public-server
---

By default, your Langflow server at `http://localhost:7860` isn't exposed to the public internet.
However, you can forward Langflow server traffic with a forwarding platform like [ngrok](https://ngrok.com/docs/getting-started/) or [zrok](https://docs.zrok.io/docs/getting-started) to make your server public.

When your Langflow server is public, you can do things like [deploy your Langflow MCP server externally](#deploy-your-mcp-server-externally), [serve API requests](#serve-api-requests), and [share a flow's **Playground** publicly](#share-a-flows-playground).

## Prerequisites

On the machine where you plan to host your Langflow installation, [install Langflow](/get-started-installation) and a reverse proxy or forwarding service.

This guide uses ngrok, but you can use any similar reverse proxy or forwarding platform.

If you want to follow along with this guide, [install ngrok](https://ngrok.com/docs/getting-started/#1-install-ngrok) and [create an ngrok authtoken](https://dashboard.ngrok.com/get-started/your-authtoken).

## Expose your Langflow server with ngrok

1. Start Langflow:

    ```bash
    uv run langflow run
    ```

2. In another terminal window, use your ngrok authtoken to authenticate your local ngrok server:

    ```bash
    ngrok config add-authtoken NGROK_AUTHTOKEN
    ```

3. Use ngrok to expose your Langflow server to the public internet:

    ```bash
    ngrok http http://localhost:7860
    ```

    This example assumes that you use the default Langflow listening address at `http://localhost:7860`. If you have a different listening address, you must modify this command accordingly.

    The ngrok session starts in your terminal and deploys an ephemeral domain with no authentication.
    To add authentication or deploy a static domain, see the [ngrok documentation](https://ngrok.com/docs/).

    The `Forwarding` line prints the forwarding address for your Langflow server:

    ```
    Forwarding https://94b1-76-64-171-14.ngrok-free.app -> http://localhost:7860
    ```

    The forwarding address acts as a reverse proxy for your Langflow server, and ngrok forwards your local traffic to this domain.

4. To verify that your Langflow server is publicly available, navigate to the forwarding address URL, such as `https://94b1-76-64-171-14.ngrok-free.app`.

## Use a public Langflow server

When your Langflow server is public, you can do things like [deploy your Langflow MCP server externally](#deploy-your-mcp-server-externally), [serve API requests](#serve-api-requests), and [share a flow's **Playground** publicly](#share-a-flows-playground).

### Deploy your MCP server externally

After you deploy a public Langflow server, you can also access your Langflow projects' MCP servers publicly.

To do this, use your server's forwarding address when you [connect a client to a Langflow MCP server](/mcp-server#connect-clients-to-use-the-servers-actions).
### Serve API requests

To send requests to a public Langflow server's [Langflow API](/api-reference-api-examples) endpoints, use the server's domain as the [base URL](/api-reference-api-examples#base-url) for your API requests.
For example:

```bash
curl -X POST \
  "PUBLIC_SERVER_DOMAIN/api/v1/webhook/FLOW_ID" \
  -H "Content-Type: application/json" \
  -H "x-api-key: LANGFLOW_API_KEY" \
  -d '{"data": "example-data"}'
```

:::tip
When you create flows on public Langflow servers, the code snippets generated in the [**API access** pane](/concepts-publish) automatically use your public server's domain.
:::

You also use your public domain when making Langflow API calls in scripts, including the code snippets that are automatically generated by Langflow.
For example, the following code snippet calls an ngrok domain to trigger the specified flow (`d764c4b8...`):

    ```python
    import requests

    url = "https://3f7c-73-64-93-151.ngrok-free.app/api/v1/run/d764c4b8-5cec-4c0f-9de0-4b419b11901a"  # The complete API endpoint URL for this flow

    # Request payload configuration
    payload = {
        "output_type": "chat",
        "input_type": "chat",
        "input_value": "Hello"
    }

    # Request headers
    headers = {
        "Content-Type": "application/json",
        "x-api-key": "LANGFLOW_API_KEY"
    }

    try:
        # Send API request
        response = requests.request("POST", url, json=payload, headers=headers)
        response.raise_for_status()  # Raise exception for bad status codes

        # Print response
        print(response.text)

    except requests.exceptions.RequestException as e:
        print(f"Error making API request: {e}")
    except ValueError as e:
        print(f"Error parsing response: {e}")
    ```

For a demo of the Langflow API in a script, see the [Quickstart](/get-started-quickstart).

### Share a flow's Playground

After you deploy a public Langflow server, you can use the **Shareable Playground** option to make a flow's **Playground** available at a public URL.
If a user accesses this URL, they can interact with the flow's chat input and output and view the results without installing Langflow or generating a Langflow API key.

For more information, see [Share a flow's Playground](/concepts-playground#share-a-flows-playground).