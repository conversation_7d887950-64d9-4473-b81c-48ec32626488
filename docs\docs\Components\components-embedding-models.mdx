---
title: Embedding Model
slug: /components-embedding-models
---

import Icon from "@site/src/components/icon";

Embedding model components in Langflow generate text embeddings using a specified Large Language Model (LLM).

Langflow includes a core **Embedding Model** component that has built-in support for some LLMs.
Alternatively, you can use any [additional embedding model component](#additional-embedding-model-components) in place of the core **Embedding Model** component.

The built-in LLMs are appropriate for most text-based embedding model use cases in Langflow.

## Use embedding model components in a flow

Use embedding model components anywhere you need to generate embeddings in a flow.

This example shows how to use an embedding model component in a flow to create a semantic search system.
This flow loads a text file, splits the text into chunks, generates embeddings for each chunk, and then loads the chunks and embeddings into a vector store. The input and output components allow a user to query the vector store through a chat interface.

![A semantic search flow that uses Embedding Model, File, Split Text, Chroma DB, Chat Input, and Chat Output components](/img/component-embedding-models-add-chat.png)

:::tip
This example uses the core **Embedding Model** component.

To use another model, you can replace the core **Embedding Model** component with any [additional embedding model component](#additional-embedding-model-components) in these steps.
However, your component might have slightly different parameters than the core **Embedding Model** component.
:::

1. Create a flow, add a **File** component, and then select a file containing text data, such as a PDF, that you can use to test the flow.

2. Add an **Embedding Model** component, and then provide a valid OpenAI API key.

    By default, the **Embedding Model** component uses an OpenAI model.
    If you want to use a different model, edit the **Model Name**, and **API Key** fields accordingly.
    Or, see [Additional embedding model components](#additional-embedding-model-components) for other embedding model components that you can use in place of the core **Embedding Model** component.

    You can enter component API keys directly or use Langflow global variables to reference your API keys.

3. Add a [**Split Text** component](/components-processing#split-text) to your flow.
This component splits text input into smaller chunks to be processed into embeddings.

4. Add a [**Vector Store** component](/components-vector-stores), such as the **Chroma DB** component, to your flow, and then configure the component to connect to your vector store database.
This component stores the generated embeddings so they can be used for similarity search.

5. Connect the components:

    * Connect the **File** component's **Loaded Files** output to the **Split Text** component's **Data or DataFrame** input.
    * Connect the **Split Text** component's **Chunks** output to the **Vector Store** component's **Ingest Data** input.
    * Connect the **Embedding Model** component's **Embeddings** output to the **Vector Store** component's **Embedding** input.

6. To query the vector store, add [**Chat Input/Output** components](/components-io#chat-io):

    * Connect the **Chat Input** component to the **Vector Store** component's **Search Query** input.
    * Connect the **Vector Store** component's **Search Results** output to the **Chat Output** component.

7. Click **Playground**, and then enter a search query to retrieve text chunks that are most semantically similar to your query.

## Embedding Model

Some **Embedding Model** component input parameters are hidden by default in the visual editor.
You can toggle parameters through the <Icon name="SlidersHorizontal" aria-hidden="true"/> **Controls** in the [component's header menu](/concepts-components#component-menus).

| Name | Display Name | Type | Description |
|------|--------------|------|-------------|
| provider | Model Provider | List | Input parameter. Select the embedding model provider. |
| model | Model Name | List | Input parameter. Select the embedding model to use.|
| api_key | OpenAI API Key | Secret[String] | Input parameter. The API key required for authenticating with the provider. |
| api_base | API Base URL | String | Input parameter. Base URL for the API. Leave empty for default. |
| dimensions | Dimensions | Integer | Input parameter. The number of dimensions for the output embeddings. |
| chunk_size | Chunk Size | Integer | Input parameter. The size of text chunks to process. Default: `1000`. |
| request_timeout | Request Timeout | Float | Input parameter. Timeout for API requests. |
| max_retries | Max Retries | Integer | Input parameter. Maximum number of retry attempts. Default: `3`. |
| show_progress_bar | Show Progress Bar | Boolean | Input parameter. Whether to display a progress bar during embedding generation. |
| model_kwargs | Model Kwargs | Dictionary | Input parameter. Additional keyword arguments to pass to the model. |
| embeddings | Embeddings | Embeddings | Output parameter. An instance for generating embeddings using the selected provider. |

## Additional embedding model components

If your provider or model isn't supported by the core **Embedding Model** component, additional single-provider embedding model components are available in the [**Bundles**](/components-bundle-components) section of the **Components** menu.

## Legacy embedding components

The following components are legacy components.
You can still use them in your flows, but they are no longer maintained and they can be removed in future releases.

<details>
<summary>Embedding Similarity</summary>

The **Embedding Similarity** component is replaced by built-in similarity search functionality in [**Vector Store** components](/components-vector-stores).

This component calculates similarity scores for two embedding vectors.

It accepts the following parameters:

| Name | Display Name | Info |
|------|--------------|------|
| embedding_vectors | Embedding Vectors | Input parameter. A list containing exactly two data objects with embedding vectors to compare. |
| similarity_metric | Similarity Metric | Input parameter. Select the similarity metric to use. Options: "Cosine Similarity", "Euclidean Distance", "Manhattan Distance". |
| similarity_data | Similarity Data | Output parameter. A data object containing the computed similarity score and additional information. |

</details>

<details>
<summary>Text Embedder</summary>

The **Text Embedder** component is replaced by the **Embedding Model** component.

This component generates embeddings for a given message using a specified embedding model.

It accepts the following parameters:

| Name | Display Name | Info |
|------|--------------|------|
| embedding_model | Embedding Model | Input parameter. The embedding model to use for generating embeddings. |
| message | Message | Input parameter. The message for which to generate embeddings. |
| embeddings | Embedding Data | Output parameter. A data object containing the original text and its embedding vector. |

</details>