---
title: Deploy Langflow on Google Cloud Platform
slug: /deployment-gcp
---

This guide demonstrates how to deploy Langflow on Google Cloud Platform with a Cloud Shell script that walks through the process of setting up a Debian-based VM with the Langflow package, Nginx, and the necessary configurations to run the Langflow development environment in GCP.

To use this script, you need a [Google Cloud](https://console.cloud.google.com/) project with the necessary permissions to create resources.

1. Follow this link to launch the Cloud Shell with the GCP deployment script from the Langflow repository:

   [![Deploy to Google Cloud](https://gstatic.com/cloudssh/images/open-btn.svg)](https://console.cloud.google.com/cloudshell/open?git_repo=https://github.com/langflow-ai/langflow&working_dir=scripts/gcp&shellonly=true&tutorial=walkthroughtutorial.md)

2. Click **Trust repo**.

    Some `gcloud` commands may not run in an ephemeral Cloud Shell environment.

3. Click **Start**, and then follow the tutorial to deploy Langflow.

:::info
This deployment uses a [spot (preemptible) instance](https://cloud.google.com/compute/docs/instances/preemptible) as a cost-effective option to demonstrate how to deploy Langflow on GCP.
However, due to the nature of spot instances, the VM can be terminated at any time if Google Cloud needs to reclaim the resources.

For a more stable deployment, consider using a regular VM instance instead of a spot instance.

For more information, see the [GCP pricing calculator](https://cloud.google.com/products/calculator?hl=en).
:::