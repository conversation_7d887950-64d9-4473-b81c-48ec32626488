---
title: Langflow release notes
slug: /release-notes
---


This page summarizes significant changes to Lang<PERSON> in each release.
For all changes, see the [Changelog](https://github.com/langflow-ai/langflow/releases/latest).

Due to strict SemVer requirements, Langflow Desktop can have different patch versions than the core Langflow OSS Python package, but the major and minor versions are aligned.

## Prepare to upgrade

:::important
Whenever possible, the Langflow team recommends installing new Langflow versions in a new virtual environment or VM before upgrading your primary installation.
This allows you to [import flows](/concepts-flows-import#import-a-flow) from your existing installation and test them in the new version without disrupting your existing installation.
In the event of breaking changes or bugs, your existing installation is preserved in a stable state.
:::

To avoid the impact of potential breaking changes and test new versions, the Langflow team recommends the following upgrade process:

1. Recommended: [Export your projects](/api-projects#export-a-project) to create backups of your flows:

    ```bash
    curl -X GET \
    "$LANGFLOW_SERVER_URL/api/v1/projects/download/$PROJECT_ID" \
      -H "accept: application/json" \
      -H "x-api-key: $LANGFLOW_API_KEY"
    ```

   To export flows from the Langflow UI, see [Import and export flows](/concepts-flows-import).

2. Install the new version:

   * **Langflow OSS Python package**: Install the new version in a new virtual environment. For instructions, see [Install and run the Langflow OSS Python package](/get-started-installation#install-and-run-the-langflow-oss-python-package).
   * **Langflow Docker image**: Run the new image in a separate container.
   * **Langflow Desktop**: To upgrade in place, open Langflow Desktop, and then click **Upgrade Available** in the header. If you want to isolate the new version, you must install Langflow Desktop on a separate physical or virtual machine, and then [import your flows](/concepts-flows-import) to the new installation.

3. [Import your flows](/concepts-flows-import) to test them in the new version, [upgrading components](/concepts-components#component-versions) as needed.

    When upgrading components, you can use the **Create backup flow before updating** option if you didn't previously export your flows.

4. If you installed the new version in isolation, upgrade your primary installation after testing the new version.

    If you made changes to your flows in the isolated installation, you might want to export and import those flows back to your upgraded primary installation so you don't have to repeat the component upgrade process.

## 1.5.0

The following updates are included in this version:

### New features and enhancements

- Authentication changes

    To enhance security and ensure proper authentication for automatic login features, most API endpoints now require authentication with a Langflow API key, regardless of the `AUTO_LOGIN` setting.
    The only exceptions are the MCP endpoints `/v1/mcp`, `/v1/mcp-projects`, and `/v2/mcp`, which never require authentication.
    For more information, see [API keys and authentication](/api-keys-and-authentication).

- Centralized **Language Model** and **Embedding Model** components

    The [**Language Model**](/components-models) and [**Embedding Model**](/components-embedding-models) components are now core components for your LLM and embeddings flows. They support multiple models and model providers, and allow you to experiment with different models without swapping out single-provider components.
    Find them in the **Components** menu in the **Models** category.

    The single-provider components are still available for your flows in the **Components** menu in the [**Bundles**](/components-bundle-components) section.

- MCP server one-click installation

    On your Langflow project's **MCP server** page, click **Auto install** to install your Langflow MCP server to MCP clients with just one click.
    The option to install with a JSON configuration file is available for macOS, Windows, and WSL.
    For more information, see [Use Langflow as an MCP server](/mcp-server).

- MCP server management

    You can now add, remove, and edit your MCP servers in the **MCP Tools** components and through your Langflow **Settings** page.
    For more information, see [Use Langflow as an MCP client](/mcp-client).

- Input schema replaces temporary overrides

    The **Input schema** pane replaces the need to manage tweak values in the **API access** pane. When you enable a parameter in the **Input schema** pane, the parameter is automatically added to your flow's code snippets, providing ready-to-use templates for making requests in your preferred programming language.

- Tool components are redistributed

    All components in the **Tools** category were moved to other core component categories, [bundles](/components-bundle-components), or marked as legacy.

    The [**MCP Tools** component](/mcp-client) is now under the **Agents** core components category.

    Tools that performed the same function were combined into single components that support multiple providers, such as the [**Web Search** component](/components-data#web-search) and the [**News Search** component](/components-data#news-search).

    For more information, see the [Tools components](/components-tools) page.

- Stability improvements

    General stability improvements and bug fixes for enhanced reliability.
    See an issue? [Raise it on GitHub](https://github.com/langflow-ai/langflow/issues).

### New integrations and bundles

- [Cleanlab integration](/integrations-cleanlab)

## 1.4.2

The following updates are included in this version:

### New features and enhancements
- Enhanced file and flow management system with improved bulk capabilities.

### New integrations and bundles
- BigQuery component for connecting to BQ datasets.
- Twelve Labs integration bundle.
- NVIDIA system assistant component.

### Deprecated features

- Deprecated the Combine text component.

## 1.4.1

The following updates are included in this version:

### New features and enhancements

- Added an enhanced "Breaking Changes" feature to help update components during version updates without breaking flows.

## 1.4.0

The following updates are included in this version:

### New features and enhancements

- Introduced MCP server functionality to serve Langflow tools to MCP-compatible clients.
- Renamed "Folders" to "Projects". The `/folders` endpoints now redirect to `/projects`.

### Deprecated features

- Deprecated the Google Gmail, Drive, and Search components.