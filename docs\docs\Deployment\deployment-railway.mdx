---
title: Deploy Langflow on Railway
description: Deploy Langflow to Railway using a one-click template
slug: /deployment-railway
---

This guide explains how to [deploy Langflow on Railway](https://railway.com/?utm_medium=integration&utm_source=docs&utm_campaign=langflow), a cloud infrastructure platform that provides auto-deploy, managed databases, and automatic scaling.

1. Create a Railway account.

   A Hobby account on Railway is sufficient for Langflow's dual-core CPU and 2 GB RAM requirements. For more information, see [Railway pricing](https://railway.com/pricing?utm_medium=integration&utm_source=docs&utm_campaign=langflow).

2. Follow this link to deploy the Langflow template on Railway:

   [![Deploy on Railway](/logos/railway-deploy.svg)](https://railway.com/new/template/JMXEWp?referralCode=MnPSdg&utm_medium=integration&utm_source=docs&utm_campaign=langflow)

3. Optional: Add any custom configuration for your Langflow deployment.

   The Langflow Railway template automatically sets up the infrastructure, deploys Langflow, and then starts the application.

4. Wait for the deployment to complete.

5. Navigate to your Langflow instance at your deployment's public URL, such as `https://APP-NAME.up.railway.app`.