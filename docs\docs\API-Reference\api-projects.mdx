---
title: Projects endpoints
slug: /api-projects
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

Use the `/projects` endpoint to create, read, update, and delete [Langflow projects](/concepts-flows#projects).

Projects store your flows and components.

## Read projects

Get a list of Langflow projects, including project IDs, names, and descriptions.

```bash
curl -X GET \
  "$LANGFLOW_URL/api/v1/projects/" \
  -H "accept: application/json" \
  -H "x-api-key: $LANGFLOW_API_KEY"
```

<details>
<summary>Result</summary>

```json
[
  {
    "name": "Starter Project",
    "description": "Manage your own projects. Download and upload projects.",
    "id": "1415de42-8f01-4f36-bf34-539f23e47466",
    "parent_id": null
  }
]
```

</details>

## Create project

Create a new project.

```bash
curl -X POST \
  "$LANGFLOW_URL/api/v1/projects/" \
  -H "Content-Type: application/json" \
  -H "x-api-key: $LANGFLOW_API_KEY" \
  -d '{
  "name": "new_project_name",
  "description": "string",
  "components_list": [],
  "flows_list": []
}'
```

<details>
<summary>Result</summary>

```json
{
  "name": "new_project_name",
  "description": "string",
  "id": "b408ddb9-6266-4431-9be8-e04a62758331",
  "parent_id": null
}
```

</details>

To add flows and components at project creation, retrieve the `components_list` and `flows_list` values from the [`/all`](/api-reference-api-examples#get-all-components) and [/flows/read](/api-flows#read-flows) endpoints and add them to the request body.

Adding a flow to a project moves the flow from its previous location. The flow is not copied.

```bash
curl -X POST \
  "$LANGFLOW_URL/api/v1/projects/" \
  -H "accept: application/json" \
  -H "Content-Type: application/json" \
  -H "x-api-key: $LANGFLOW_API_KEY" \
  -d '{
  "name": "new_project_name",
  "description": "string",
  "components_list": [
    "3fa85f64-5717-4562-b3fc-2c963f66afa6"
  ],
  "flows_list": [
    "3fa85f64-5717-4562-b3fc-2c963f66afa6"
  ]
}'
```

## Read project

Retrieve details of a specific project.

To find the UUID of your project, call the [read projects](#read-projects) endpoint.

```bash
curl -X GET \
  "$LANGFLOW_URL/api/v1/projects/$PROJECT_ID" \
  -H "accept: application/json" \
  -H "x-api-key: $LANGFLOW_API_KEY"
```

<details>
<summary>Result</summary>

```json
[
  {
    "name": "Starter Project",
    "description": "Manage your own projects. Download and upload projects.",
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    "parent_id": null
  }
]
```

</details>

## Update project

Update the information of a specific project with a `PATCH` request.

Each PATCH request updates the project with the values you send.
Only the fields you include in your request are updated.
If you send the same values multiple times, the update is still processed, even if the values are unchanged.

```bash
curl -X PATCH \
  "$LANGFLOW_URL/api/v1/projects/b408ddb9-6266-4431-9be8-e04a62758331" \
  -H "accept: application/json" \
  -H "x-api-key: $LANGFLOW_API_KEY" \
  -d '{
  "name": "string",
  "description": "string",
  "parent_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "components": [
    "3fa85f64-5717-4562-b3fc-2c963f66afa6"
  ],
  "flows": [
    "3fa85f64-5717-4562-b3fc-2c963f66afa6"
  ]
}'
```

<details>
<summary>Result</summary>

```json
{
  "name": "string",
  "description": "string",
  "id": "b408ddb9-6266-4431-9be8-e04a62758331",
  "parent_id": null
}
```

</details>

## Delete project

Delete a specific project.

```bash
curl -X DELETE \
  "$LANGFLOW_URL/api/v1/projects/$PROJECT_ID" \
  -H "accept: */*" \
  -H "x-api-key: $LANGFLOW_API_KEY"
```

<details>
<summary>Result</summary>

```text
204 No Content
```

</details>

## Export a project

Download all flows from a project as a zip file.

The `--output` flag is optional.

```bash
curl -X GET \
  "$LANGFLOW_URL/api/v1/projects/download/$PROJECT_ID" \
  -H "accept: application/json" \
  -H "x-api-key: $LANGFLOW_API_KEY" \
  --output langflow-project.zip
```

## Import a project

Import a project and its flows by uploading a Langflow project zip file:

```bash
curl -X POST \
  "$LANGFLOW_URL/api/v1/projects/upload/" \
  -H "accept: application/json" \
  -H "Content-Type: multipart/form-data" \
  -H "x-api-key: $LANGFLOW_API_KEY" \
  -F "file=@20241230_135006_langflow_flows.zip;type=application/zip"
```