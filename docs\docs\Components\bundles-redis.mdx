---
title: Redis
slug: /bundles-redis
---

import Icon from "@site/src/components/icon";

[Bundles](/components-bundle-components) contain custom components that support specific third-party integrations with Langflow.

This page describes the components that are available in the **Redis** bundle.

## Redis Chat Memory

The **Redis Chat Memory** component retrieves and stores chat messages using Redis memory storage.

Chat memories are passed between memory storage components as the [`Memory`](/data-types#memory) data type.

For more information about using external chat memory in flows, see the [**Message History** component](/components-helpers#message-history).

### Redis Chat Memory parameters

Many **Redis Chat Memory** component input parameters are hidden by default in the visual editor.
You can toggle parameters through the <Icon name="SlidersHorizontal" aria-hidden="true"/> **Controls** in the [component's header menu](/concepts-components#component-menus).

| Name | Display Name | Info |
|------|--------------|------|
| host | hostname | Input parameter. The IP address or hostname. |
| port | port | Input parameter. The Redis Port Number. |
| database | database | Input parameter. The Redis database. |
| username | Username | Input parameter. The Redis username. |
| password | Password | Input parameter. The password for the username. |
| key_prefix | Key prefix | Input parameter. The key prefix. |
| session_id | Session ID | Input parameter. The unique session identifier for the message. |

## Redis vector store

See [**Redis** vector store component](/components-vector-stores#redis).