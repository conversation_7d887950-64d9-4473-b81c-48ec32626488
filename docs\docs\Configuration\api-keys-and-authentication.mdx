---
title: API keys and authentication
slug: /api-keys-and-authentication
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';
import Icon from "@site/src/components/icon";

:::warning
Never expose Langflow ports directly to the internet without proper security measures.
Set `LANGFLOW_AUTO_LOGIN=False`, use a non-default `LANGFLOW_SECRET_KEY`, and deploy your Langflow server behind a reverse proxy with authentication enabled.
For more information, see [Start a Langflow server with authentication enabled](#start-a-langflow-server-with-authentication-enabled).
:::

Authentication credentials help prevent unauthorized access to your Langflow server, flows, and services connected through components.

There are three types of credentials that you use in Langflow:

* [Langflow API keys](#langflow-api-keys): For authentication with the Langflow API and authorizing server-side Langflow actions like running flows and uploading files.
* [Component API keys](#component-api-keys): For authentication between Langflow and a service connected through a component, such as a model provider or third-party API.
* [Authentication environment variables](#authentication-environment-variables): These environment variables configure how Langflow handles user authentication and authorization.

## Langflow API keys {#langflow-api-keys}

You can use Langflow API keys to interact with Langflow programmatically.

A Langflow API key has the same permissions and access as the user who created it.
This means your API key can only access your own flows, components, and database.
You cannot access other users' resources with your own Langflow API keys.

If you create a key as a superuser, then that key has superuser privileges within your Langflow server.

In Langflow version 1.5 and later, most API endpoints require a Langflow API key, even when `AUTO_LOGIN` is `True`.
For more information, see [`LANGFLOW_AUTO_LOGIN`](#langflow-auto-login).

### Create a Langflow API key

You can generate a Langflow API key with the UI or the CLI.

The UI-generated key is appropriate for most cases. The CLI-generated key is needed when your Langflow server is running in `--backend-only` mode.

<Tabs>
<TabItem value="Langflow UI" label="Langflow UI" default>

1. In the Langflow UI header, click your profile icon, and then select **Settings**.
2. Click **Langflow API Keys**, and then click **Add New**.
3. Name your key, and then click **Create API Key**.
4. Copy the API key and store it securely.

</TabItem>
<TabItem value="Langflow CLI" label="Langflow CLI">

If you're serving your flow with `--backend-only=true`, you can't create API keys in the UI, because the frontend is not running.

Depending on your authentication settings, note the following requirements for creating API keys with the Langflow CLI:

* If `AUTO_LOGIN` is `FALSE`, you must be logged in as a superuser.
* If `AUTO LOGIN` is `TRUE`, you're already logged in as superuser.

To create an API key for a user from the CLI, do the following:

1. In your `.env` file, set `AUTO_LOGIN=FALSE`, and set superuser credentials for your server.

    ```text
    LANGFLOW_AUTO_LOGIN=False
    LANGFLOW_SUPERUSER=administrator
    LANGFLOW_SUPERUSER_PASSWORD=securepassword
    ```

2. To confirm your superuser status, call [`GET /users/whoami`](/api-users#get-current-user), and then check that the response contains `"is_superuser": true`:

    ```bash
    curl -X GET \
      "$LANGFLOW_URL/api/v1/users/whoami" \
      -H "accept: application/json" \
      -H "x-api-key: $LANGFLOW_API_KEY"
    ```

    <details>
    <summary>Result</summary>

    ```json
    {
      "id": "07e5b864-e367-4f52-b647-a48035ae7e5e",
      "username": "langflow",
      "profile_image": null,
      "store_api_key": null,
      "is_active": true,
      "is_superuser": true,
      "create_at": "2025-05-08T17:59:07.855965",
      "updated_at": "2025-05-29T15:06:56.157860",
      "last_login_at": "2025-05-29T15:06:56.157016",
    }
    ```

    </details>

3. Create an API key:

    ```shell
    uv run langflow api-key
    ```

</TabItem>
</Tabs>

### Use a Langflow API key

To authenticate Langflow API requests, pass your Langflow API key an `x-api-key` header or query parameter.

<Tabs>
<TabItem value="header" label="HTTP header" default>

```shell
curl -X POST \
  "http://$LANGFLOW_SERVER_ADDRESS/api/v1/run/$FLOW_ID?stream=false" \
  -H "Content-Type: application/json" \
  -H "x-api-key: $LANGFLOW_API_KEY" \
  -d '{"inputs": {"text":""}, "tweaks": {}}'
```

</TabItem>
<TabItem value="parameter" label="Query parameter">

```shell
curl -X POST \
  "http://$LANGFLOW_SERVER_ADDRESS/api/v1/run/$FLOW_ID?x-api-key=$LANGFLOW_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"inputs": {"text":""}, "tweaks": {}}'
```

</TabItem>
</Tabs>

For more information about forming Langflow API requests, see [Get started with the Langflow API](/api-reference-api-examples) and [Trigger flows with the Langflow API](/concepts-publish).

### Revoke an API key

To revoke and delete an API key, do the following:

1. In the Langflow UI header, click your profile icon, and then select **Settings**.
2. Click **Langflow API Keys**.
3. Select the keys you want to delete, and then click <Icon name="Trash2" aria-hidden="true"/> **Delete**.

This action immediately invalidates the key and prevents it from being used again.

## Component API keys

Component API keys authorize access to external services that are called by components in your flows. These are not general application credentials, but specifically the API keys required by individual components to connect to services like model providers, databases, or third-party APIs.

Component API keys are added as global variables with the Langflow UI, or sourced from the `.env` file. When stored as global variables, use the **Credential** type for secure handling of sensitive information.

Component API keys are created and managed within the service provider's platform. Deleting a global variable from Langflow does not delete or invalidate the actual API key in the service provider's system. You must delete or rotate component API keys directly within the service provider's interface or API. Langflow only stores the encrypted value of your keys; it does not have control over the actual credentials.

For more information, see [Global variables](/configuration-global-variables).

## Authentication environment variables

This section describes the available authentication configuration variables.

You can use the [`.env.example`](https://github.com/langflow-ai/langflow/blob/main/.env.example) file in the Langflow repository as a template for your own `.env` file.

### LANGFLOW_AUTO_LOGIN {#langflow-auto-login}

Langflow doesn't allow users to have simultaneous or shared access to flows in the Langflow UI.

If `LANGFLOW_AUTO_LOGIN=False`, automatic login is disabled. The login form is required to access the Langflow UI, and Langflow API requests require a Langflow API key.
If `false`, you must also set [`LANGFLOW_SUPERUSER` and `LANGFLOW__SUPERUSER_PASSWORD`](#langflow-superuser).

If `LANGFLOW_AUTO_LOGIN=True`, Langflow bypasses UI authentication.
If you also disable user management (`LANGFLOW_NEW_USER_IS_ACTIVE=true`), users can access the same environment without password protection. If two users access the same flow, Langflow saves only the work of the most recent user.

#### AUTO_LOGIN and API authentication in version 1.5 and later

In Langflow versions 1.5 and later, most API endpoints require a Langflow API key, even when `AUTO_LOGIN` is `True`.
The only exceptions are the MCP endpoints `/v1/mcp`, `/v1/mcp-projects`, and `/v2/mcp`, which never require authentication.

<details>
<summary>LANGFLOW_AUTO_LOGIN and LANGFLOW_SKIP_AUTH_AUTO_LOGIN options</summary>

In Langflow versions earlier than 1.5, if `LANGFLOW_AUTO_LOGIN=true`, then Langflow automatically logs users in as a superuser without requiring authentication.
In this case, API requests don't require a Langflow API key.

In Langflow version 1.5, you can set `LANGFLOW_SKIP_AUTH_AUTO_LOGIN=true` _and_ `LANGFLOW_AUTO_LOGIN=true` to skip authentication for API requests _and_ allow automatic login as a superuser for all users.
This is a temporary bypass for backwards compatibility, and the `LANGFLOW_SKIP_AUTH_AUTO_LOGIN` option will be removed in a future release.

If either `LANGFLOW_AUTO_LOGIN` or `LANGFLOW_SKIP_AUTH_AUTO_LOGIN` are set to `false`, then authentication is required.
</details>

### LANGFLOW_SUPERUSER and LANGFLOW_SUPERUSER_PASSWORD {#langflow-superuser}

These variables specify the username and password for the Langflow server's superuser.

```text
LANGFLOW_SUPERUSER=administrator
LANGFLOW_SUPERUSER_PASSWORD=securepassword
```

They are required if `LANGFLOW_AUTO_LOGIN=false`.
Otherwise, they aren't relevant.

If required and not set, the default values are `langflow` and `langflow`.

For more information, see [Start a Langflow server with authentication enabled](#start-a-langflow-server-with-authentication-enabled) and the CLI command [`langflow superuser`](/configuration-cli#langflow-superuser).

### LANGFLOW_SECRET_KEY {#langflow-secret-key}

This environment variable stores a secret key used for encrypting sensitive data like API keys.
Langflow uses the [Fernet](https://pypi.org/project/cryptography/) library for secret key encryption.

If no secret key is provided, Langflow automatically generates one.

However, you should generate and explicitly set your own key in production environments.
This is particularly important for multi-instance deployments like Kubernetes to ensure consistent encryption across instances.

To generate a secret encryption key for `LANGFLOW_SECRET_KEY`, do the following:

1. Run the command to generate and copy a secret to the clipboard.

    <Tabs>
    <TabItem value="unix" label="macOS or Linux">

    * **macOS**: Generate a secret key and copy it to the clipboard:

        ```bash
        python3 -c "from secrets import token_urlsafe; print(f'LANGFLOW_SECRET_KEY={token_urlsafe(32)}')" | pbcopy
        ```

    * **Linux**: Generate a secret key and copy it to the clipboard:

        ```bash
        python3 -c "from secrets import token_urlsafe; print(f'LANGFLOW_SECRET_KEY={token_urlsafe(32)}')" | xclip -selection clipboard
        ```

    * **Unix**: Generate a secret key and print it to the terminal to manually copy it:

        ```bash
        python3 -c "from secrets import token_urlsafe; print(f'LANGFLOW_SECRET_KEY={token_urlsafe(32)}')"
        ```

    </TabItem>
    <TabItem value="windows" label="Windows">

    * Generate a secret key and copy it to the clipboard:

        ```bash
        python -c "from secrets import token_urlsafe; print(f'LANGFLOW_SECRET_KEY={token_urlsafe(32)}')"
        ```

    * Generate a secret key and print it to the terminal to manually copy it:

        ```bash

        # Or just print
        python -c "from secrets import token_urlsafe; print(f'LANGFLOW_SECRET_KEY={token_urlsafe(32)}')"
        ```

    </TabItem>
    </Tabs>

2. Paste the value into your `.env` file:

    ```text
    LANGFLOW_SECRET_KEY=dBuu...2kM2_fb
    ```

### LANGFLOW_NEW_USER_IS_ACTIVE {#langflow-new-user-is-active}

When `LANGFLOW_NEW_USER_IS_ACTIVE=False` (default), a superuser must explicitly activate a new user's account before they can sign in to the Langflow UI.
The superuser can also deactivate a user's account as needed.

When `LANGFLOW_NEW_USER_IS_ACTIVE=True`, new user accounts are automatically activated.

```text
LANGFLOW_NEW_USER_IS_ACTIVE=False
```

For more information, see [Start a Langflow server with authentication enabled](#start-a-langflow-server-with-authentication-enabled).

## Start a Langflow server with authentication enabled

This section shows you how to use the [authentication environment variables](/api-keys-and-authentication#authentication-environment-variables) to deploy a Langflow server with authentication enabled.
This involves disabling automatic login, setting superuser credentials, generating a secret encryption key, and enabling user management.

This configuration is recommended for any deployment where Langflow is exposed to a shared or public network, or where multiple users access the same Langflow server.

With authentication enabled, all users must sign in to the Langflow UI with valid credentials, and API requests require authentication with a Langflow API key.
Additionally, you must sign in as a superuser to manage users and [create a Langflow API key](#create-a-langflow-api-key) with superuser privileges.

### Start the Langflow server

1. Create a `.env` file with the following variables:

    ```text
    LANGFLOW_AUTO_LOGIN=False
    LANGFLOW_SUPERUSER=
    LANGFLOW_SUPERUSER_PASSWORD=
    LANGFLOW_SECRET_KEY=
    LANGFLOW_NEW_USER_IS_ACTIVE=False
    ```

    Your `.env` file can have other environment variables.
    This example focuses on authentication variables.

2. Set `LANGFLOW_SUPERUSER` and `LANGFLOW_SUPERUSER_PASSWORD` to your desired superuser credentials.

    For a one-time test, you can use basic credentials like `administrator` and `password`.
    Strong, securely-stored credentials are recommended for true development and production environments.

3. Recommended: Generate and set a `LANGFLOW_SECRET_KEY` for encrypting sensitive data.

    If you don't set a secret key, Langflow generates one automatically, but this isn't recommended for production environments.

    For instructions on generating at setting a secret key, see [`LANGFLOW_SECRET_KEY`](#langflow-secret-key).

4. Save your `.env` file with the populated variables. For example:

    ```text
    LANGFLOW_AUTO_LOGIN=False
    LANGFLOW_SUPERUSER=administrator
    LANGFLOW_SUPERUSER_PASSWORD=securepassword
    LANGFLOW_SECRET_KEY=dBuu...2kM2_fb
    LANGFLOW_NEW_USER_IS_ACTIVE=False
    ```

5. Start Langflow with the configuration from your `.env` file:

    ```text
    uv run langflow run --env-file .env
    ```

6. Verify the server is running. The default location is `http://localhost:7860`.

Next, you can add users to your Langflow server to collaborate with others on flows.

### Manage users as an administrator

1. To complete your first-time login as a superuser, go to `http://localhost:7860/login`.

    If you aren't using the default location, replace `localhost:7860` with your server's address.

2. Log in with the superuser credentials you set in your `.env` (`LANGFLOW_SUPERUSER` and `LANGFLOW_SUPERUSER_PASSWORD`).

3. To manage users on your server, navigate to `/admin`, such as `http://localhost:7860/admin`, click your user profile image, and then click **Admin Page**.

    As a superuser, you can add users, set permissions, reset passwords, and delete accounts.

4. To add a user, click **New User**, and then complete the user account form:

    1. Enter a username and password.
    2. To activate the account immediately, select **Active**. Inactive users cannot sign in or access flows they created before becoming inactive.
    3. Deselect **Superuser** if you don't want the user to have full administrative privileges.
    4. Click **Save**. The new user appears in the **Admin Page**.

5. To test the new user's access, sign out of Langflow, and then sign in with the new user's credentials.

    Try to access the `/admin` page.
    You are redirected to the `/flows` page if the new user isn't a superuser.